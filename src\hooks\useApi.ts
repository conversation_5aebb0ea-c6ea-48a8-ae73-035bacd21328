import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useWebSocketContext } from '@/components/providers/WebSocketProvider';
import { apiClient } from '@/lib/api';
import { 
  ArbitrageOpportunity, 
  Trade, 
  Token, 
  PerformanceMetrics, 
  MLStrategy, 
  LearningEvent, 
  SystemHealth, 
  NetworkStatus,
  FlashLoanQuote,
  ExecutionQueue
} from '@/types';

// Query keys
export const queryKeys = {
  health: ['health'],
  opportunities: (params?: any) => ['opportunities', params],
  trades: (params?: any) => ['trades', params],
  tokens: (params?: any) => ['tokens', params],
  performance: ['performance'],
  mlStrategies: (params?: any) => ['ml-strategies', params],
  learningEvents: (params?: any) => ['learning-events', params],
  mlStats: ['ml-stats'],
  systemHealth: ['system-health'],
  networkStatus: ['network-status'],
  flashLoanQuotes: (params: any) => ['flash-loan-quotes', params],
  executionQueue: ['execution-queue'],
  mevProtection: ['mev-protection'],
  databaseHealth: ['database-health'],
  performanceStats: ['performance-stats'],
  cacheStats: ['cache-stats'],
  multiChainData: ['multi-chain-data'],
  priceData: (params?: any) => ['price-data', params],
};

// Health check hook
export function useHealth() {
  return useQuery({
    queryKey: queryKeys.health,
    queryFn: apiClient.getHealth,
    staleTime: 1000 * 30, // 30 seconds
    refetchInterval: 1000 * 60, // 1 minute
  });
}

// Opportunities hook with real-time updates
export function useOpportunities(params?: { limit?: number; offset?: number }) {
  const queryClient = useQueryClient();
  const { lastMessage, subscribe, unsubscribe } = useWebSocketContext();

  const query = useQuery({
    queryKey: queryKeys.opportunities(params),
    queryFn: () => apiClient.getOpportunities(params),
    staleTime: 1000 * 5, // 5 seconds for critical data
    refetchInterval: 1000 * 10, // 10 seconds fallback
  });

  // Subscribe to real-time updates
  useEffect(() => {
    subscribe('opportunity.updated');
    return () => unsubscribe('opportunity.updated');
  }, [subscribe, unsubscribe]);

  // Handle real-time updates
  useEffect(() => {
    if (lastMessage?.type === 'opportunity.updated') {
      queryClient.setQueryData(
        queryKeys.opportunities(params),
        lastMessage.data as ArbitrageOpportunity[]
      );
    }
  }, [lastMessage, queryClient, params]);

  return query;
}

// Trades hook with real-time updates
export function useTrades(params?: { limit?: number; offset?: number }) {
  const queryClient = useQueryClient();
  const { lastMessage, subscribe, unsubscribe } = useWebSocketContext();

  const query = useQuery({
    queryKey: queryKeys.trades(params),
    queryFn: () => apiClient.getTrades(params),
    staleTime: 1000 * 10, // 10 seconds
    refetchInterval: 1000 * 30, // 30 seconds fallback
  });

  // Subscribe to real-time updates
  useEffect(() => {
    subscribe('trade.updated');
    return () => unsubscribe('trade.updated');
  }, [subscribe, unsubscribe]);

  // Handle real-time updates
  useEffect(() => {
    if (lastMessage?.type === 'trade.updated') {
      queryClient.setQueryData(
        queryKeys.trades(params),
        lastMessage.data as Trade[]
      );
    }
  }, [lastMessage, queryClient, params]);

  return query;
}

// Tokens hook
export function useTokens(params?: { limit?: number; offset?: number; network?: string }) {
  return useQuery({
    queryKey: queryKeys.tokens(params),
    queryFn: () => apiClient.getTokens(params),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchInterval: 1000 * 60 * 10, // 10 minutes
  });
}

// Performance metrics hook
export function usePerformanceMetrics() {
  const queryClient = useQueryClient();
  const { lastMessage, subscribe, unsubscribe } = useWebSocketContext();

  const query = useQuery({
    queryKey: queryKeys.performance,
    queryFn: apiClient.getPerformanceMetrics,
    staleTime: 1000 * 30, // 30 seconds
    refetchInterval: 1000 * 60, // 1 minute
  });

  // Subscribe to real-time updates
  useEffect(() => {
    subscribe('metrics.updated');
    return () => unsubscribe('metrics.updated');
  }, [subscribe, unsubscribe]);

  // Handle real-time updates
  useEffect(() => {
    if (lastMessage?.type === 'metrics.updated') {
      queryClient.setQueryData(
        queryKeys.performance,
        lastMessage.data as PerformanceMetrics
      );
    }
  }, [lastMessage, queryClient]);

  return query;
}

// ML Strategies hook
export function useMLStrategies(params?: { limit?: number; offset?: number }) {
  return useQuery({
    queryKey: queryKeys.mlStrategies(params),
    queryFn: () => apiClient.getMLStrategies(params),
    staleTime: 1000 * 60 * 2, // 2 minutes
    refetchInterval: 1000 * 60 * 5, // 5 minutes
  });
}

// Learning Events hook
export function useLearningEvents(params?: { limit?: number; offset?: number }) {
  return useQuery({
    queryKey: queryKeys.learningEvents(params),
    queryFn: () => apiClient.getLearningEvents(params),
    staleTime: 1000 * 30, // 30 seconds
    refetchInterval: 1000 * 60, // 1 minute
  });
}

// ML Stats hook
export function useMLStats() {
  return useQuery({
    queryKey: queryKeys.mlStats,
    queryFn: apiClient.getMLStats,
    staleTime: 1000 * 60, // 1 minute
    refetchInterval: 1000 * 60 * 2, // 2 minutes
  });
}

// System Health hook
export function useSystemHealth() {
  const queryClient = useQueryClient();
  const { lastMessage, subscribe, unsubscribe } = useWebSocketContext();

  const query = useQuery({
    queryKey: queryKeys.systemHealth,
    queryFn: apiClient.getSystemHealth,
    staleTime: 1000 * 15, // 15 seconds
    refetchInterval: 1000 * 30, // 30 seconds
  });

  // Subscribe to real-time updates
  useEffect(() => {
    subscribe('system.health');
    return () => unsubscribe('system.health');
  }, [subscribe, unsubscribe]);

  // Handle real-time updates
  useEffect(() => {
    if (lastMessage?.type === 'system.health') {
      queryClient.setQueryData(
        queryKeys.systemHealth,
        lastMessage.data as SystemHealth
      );
    }
  }, [lastMessage, queryClient]);

  return query;
}

// Network Status hook
export function useNetworkStatus() {
  return useQuery({
    queryKey: queryKeys.networkStatus,
    queryFn: apiClient.getNetworkStatus,
    staleTime: 1000 * 30, // 30 seconds
    refetchInterval: 1000 * 60, // 1 minute
  });
}

// Execution Queue hook
export function useExecutionQueue() {
  const queryClient = useQueryClient();
  const { lastMessage, subscribe, unsubscribe } = useWebSocketContext();

  const query = useQuery({
    queryKey: queryKeys.executionQueue,
    queryFn: apiClient.getExecutionQueue,
    staleTime: 1000 * 5, // 5 seconds for critical data
    refetchInterval: 1000 * 10, // 10 seconds
  });

  // Subscribe to real-time updates
  useEffect(() => {
    subscribe('queue.updated');
    return () => unsubscribe('queue.updated');
  }, [subscribe, unsubscribe]);

  // Handle real-time updates
  useEffect(() => {
    if (lastMessage?.type === 'queue.updated') {
      queryClient.setQueryData(
        queryKeys.executionQueue,
        lastMessage.data as ExecutionQueue[]
      );
    }
  }, [lastMessage, queryClient]);

  return query;
}

// MEV Protection Status hook
export function useMEVProtectionStatus() {
  return useQuery({
    queryKey: queryKeys.mevProtection,
    queryFn: apiClient.getMEVProtectionStatus,
    staleTime: 1000 * 60, // 1 minute
    refetchInterval: 1000 * 60 * 2, // 2 minutes
  });
}

// Flash Loan Quotes hook
export function useFlashLoanQuotes(params: { asset: string; amount: string; network: string }) {
  return useQuery({
    queryKey: queryKeys.flashLoanQuotes(params),
    queryFn: () => apiClient.getFlashLoanQuotes(params),
    staleTime: 1000 * 10, // 10 seconds
    refetchInterval: 1000 * 30, // 30 seconds
    enabled: !!(params.asset && params.amount && params.network),
  });
}

// Price Data hook
export function usePriceData(params?: { 
  tokens?: string[]; 
  networks?: string[]; 
  timeframe?: string 
}) {
  return useQuery({
    queryKey: queryKeys.priceData(params),
    queryFn: () => apiClient.getPriceData(params),
    staleTime: 1000 * 15, // 15 seconds
    refetchInterval: 1000 * 30, // 30 seconds
  });
}
